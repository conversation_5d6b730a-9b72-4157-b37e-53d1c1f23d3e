/* General Styles */
body {
    font-family: 'Poppins', sans-serif;
    margin: 0;
    line-height: 1.6;
    background-color: #f8fafc;
    color: #333;
}

.container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 50px;
    margin-right: 15px;
}

.logo h1 {
    margin: 0;
    font-size: 1.8rem;
    color: #2c3e50;
}

nav a {
    text-decoration: none;
    color: #555;
    font-weight: 600;
    margin-left: 25px;
    transition: color 0.3s;
}

nav a:hover {
    color: #6B9BD1; /* App's primary color */
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(107, 155, 209, 0.8), rgba(74, 123, 167, 0.9)), url('https://source.unsplash.com/random/1600x900/?nursery,kids') no-repeat center center/cover;
    color: #fff;
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero .container {
    position: relative;
    z-index: 2;
}

.hero h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.2rem;
    max-width: 600px;
    margin: 0 auto 2rem;
}

.cta-button {
    background-color: #FFA726; /* App's secondary color */
    color: #fff;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: background-color 0.3s, transform 0.3s;
}

.cta-button:hover {
    background-color: #F57C00;
    transform: translateY(-3px);
}

/* Typed Text Animation */
.typed-text {
    color: #FFA726;
}

.cursor {
    display: inline-block;
    background-color: #fff;
    margin-left: 0.1rem;
    width: 3px;
    animation: blink 1s infinite;
}

@keyframes blink {
    0% { background-color: #fff; }
    49% { background-color: #fff; }
    50% { background-color: transparent; }
    99% { background-color: transparent; }
    100% { background-color: #fff; }
}

/* Floating Icons */
.hero-icons {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-icons i {
    position: absolute;
    display: block;
    font-size: 3rem; /* Emojis as icons */
    color: rgba(255, 255, 255, 0.15);
    animation: float 20s infinite linear;
    bottom: -150px; /* Start from bottom */
    user-select: none;
}

@keyframes float {
    from {
        transform: translateY(0) rotate(0deg);
    }
    to {
        transform: translateY(-120vh) rotate(360deg);
        opacity: 0;
    }
}

/* Features Section */
.features {
    padding: 60px 0;
    text-align: center;
}

.features h3 {
    font-size: 2.5rem;
    margin-bottom: 50px;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.feature-item {
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    text-align: center;
}

.feature-icon {
    display: inline-block;
    background-color: #6B9BD1;
    color: #fff;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    line-height: 70px;
    font-size: 2.5rem;
    margin-bottom: 20px;
    transition: transform 0.3s;
}

.feature-item:hover .feature-icon {
    transform: scale(1.1);
}

.feature-item:nth-child(2) .feature-icon { background-color: #FFA726; }
.feature-item:nth-child(3) .feature-icon { background-color: #81C784; }

.feature-item h4 {
    font-size: 1.5rem;
    color: #2c3e50;
    margin-bottom: 15px;
}

/* Pricing Section */
.pricing {
    background: #f8fafc;
    padding: 60px 0;
    text-align: center;
}

.pricing h3 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.pricing > .container > p {
    font-size: 1.1rem;
    color: #777;
    margin-bottom: 50px;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    align-items: center;
}

.pricing-card {
    background: #fff;
    padding: 40px 30px;
    border-radius: 10px;
    border: 1px solid #eee;
    transition: transform 0.3s, box-shadow 0.3s;
    position: relative;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}

.pricing-card h4 {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: #2c3e50;
}

.pricing-card .price {
    font-size: 1.2rem;
    font-weight: 600;
    color: #6B9BD1;
    margin-bottom: 30px;
}

.pricing-card .price span {
    font-size: 2.5rem;
    font-weight: 700;
}

.pricing-card ul {
    list-style: none;
    padding: 0;
    margin-bottom: 40px;
    text-align: left;
}

.pricing-card ul li {
    margin-bottom: 15px;
    color: #555;
    position: relative;
    padding-left: 25px;
}

.pricing-card ul li::before {
    content: '✔';
    color: #81C784;
    position: absolute;
    left: 0;
}

.pricing-card.popular {
    transform: scale(1.05);
    border: 2px solid #FFA726;
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #FFA726;
    color: #fff;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    z-index: 1;
}

/* Download Section */
.download {
    background-color: #2c3e50;
    color: #fff;
    padding: 60px 0;
    text-align: center;
}

.download h3 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.download p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.download-button {
    background-color: #fff;
    color: #2c3e50;
    padding: 18px 40px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.2rem;
    transition: background-color 0.3s, color 0.3s, transform 0.3s;
    display: inline-block;
}

.download-button:hover {
    background-color: #FFA726;
    color: #fff;
    transform: translateY(-3px);
}

/* Footer */
footer {
    background: #f4f4f4;
    text-align: center;
    padding: 20px 0;
    margin-top: 0;
} 